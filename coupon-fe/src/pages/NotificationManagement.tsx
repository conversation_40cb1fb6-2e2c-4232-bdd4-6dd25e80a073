import { useEffect, useContext } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Link } from "@tanstack/react-router";
import {
  Bell,
  Send,
  FileText,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  Settings,
  BarChart3,
} from "lucide-react";
import { LayoutContext } from "@/routes/__root";
import { useNotificationAnalytics, useAdminListNotifications } from "@/services/notifications";

const NotificationManagement = () => {
  const { setActiveTab } = useContext(LayoutContext);
  
  useEffect(() => {
    setActiveTab("notifications");
  }, [setActiveTab]);

  // Mock data - replace with actual API calls
  const stats = {
    totalNotifications: 1247,
    deliveryRate: 94.2,
    activeTemplates: 12,
    engagementRate: 68.5,
  };

  const recentNotifications = [
    {
      id: 1,
      title: "New Voucher Available: SAVE20",
      type: "VOUCHER_CREATED",
      status: "SENT",
      recipient: "All Premium Users",
      sentAt: "2024-01-15 14:30",
      readCount: 245,
      totalSent: 320,
    },
    {
      id: 2,
      title: "Voucher Expiring Soon: WINTER50",
      type: "VOUCHER_EXPIRING",
      status: "PENDING",
      recipient: "Eligible Users",
      scheduledAt: "2024-01-16 09:00",
      readCount: 0,
      totalSent: 0,
    },
    {
      id: 3,
      title: "Welcome to Coupon System",
      type: "USER_WELCOME",
      status: "SENT",
      recipient: "New Users",
      sentAt: "2024-01-15 12:15",
      readCount: 89,
      totalSent: 156,
    },
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "SENT":
        return <Badge variant="default" className="bg-green-100 text-green-800">Sent</Badge>;
      case "PENDING":
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case "FAILED":
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "VOUCHER_CREATED":
        return <FileText className="h-4 w-4 text-blue-600" />;
      case "VOUCHER_EXPIRING":
        return <Clock className="h-4 w-4 text-orange-600" />;
      case "USER_WELCOME":
        return <Users className="h-4 w-4 text-green-600" />;
      default:
        return <Bell className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="min-h-full bg-gradient-to-br from-blue-50 to-indigo-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Notification Management
            </h1>
            <p className="text-gray-600 mt-2">
              Manage templates, send notifications, and track engagement
            </p>
          </div>
          <div className="flex space-x-3">
            <Button asChild>
              <Link to="/notifications/templates/create">
                <Plus className="h-4 w-4 mr-2" />
                New Template
              </Link>
            </Button>
            <Button asChild variant="outline">
              <Link to="/notifications/send">
                <Send className="h-4 w-4 mr-2" />
                Send Notification
              </Link>
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Notifications
              </CardTitle>
              <Bell className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalNotifications.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                +12% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Delivery Rate
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.deliveryRate}%</div>
              <p className="text-xs text-muted-foreground">
                +2.1% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Templates
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeTemplates}</div>
              <p className="text-xs text-muted-foreground">
                +3 new this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Engagement Rate
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.engagementRate}%</div>
              <p className="text-xs text-muted-foreground">
                +5.2% from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions and Recent Notifications */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-blue-600" />
                <span>Quick Actions</span>
              </CardTitle>
              <CardDescription>
                Common notification management tasks
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button asChild className="w-full justify-start">
                <Link to="/notifications/templates">
                  <FileText className="h-4 w-4 mr-2" />
                  Manage Templates
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link to="/notifications/analytics">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Analytics
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link to="/notifications/scheduled">
                  <Clock className="h-4 w-4 mr-2" />
                  Scheduled Notifications
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link to="/notifications/history">
                  <Bell className="h-4 w-4 mr-2" />
                  Notification History
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Recent Notifications */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center space-x-2">
                  <Bell className="h-5 w-5 text-green-600" />
                  <span>Recent Notifications</span>
                </span>
                <Button asChild variant="outline" size="sm">
                  <Link to="/notifications/history">View All</Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Latest notification activity and status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Notification</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Engagement</TableHead>
                    <TableHead>Time</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentNotifications.map((notification) => (
                    <TableRow key={notification.id}>
                      <TableCell>
                        <div className="flex items-start space-x-3">
                          {getTypeIcon(notification.type)}
                          <div>
                            <div className="font-medium text-sm">
                              {notification.title}
                            </div>
                            <div className="text-xs text-gray-500">
                              {notification.recipient}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(notification.status)}
                      </TableCell>
                      <TableCell>
                        {notification.status === "SENT" ? (
                          <div className="text-sm">
                            <div className="font-medium">
                              {notification.readCount}/{notification.totalSent}
                            </div>
                            <div className="text-xs text-gray-500">
                              {Math.round((notification.readCount / notification.totalSent) * 100)}% read
                            </div>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {notification.sentAt || notification.scheduledAt}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default NotificationManagement;
