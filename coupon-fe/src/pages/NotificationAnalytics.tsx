import { useState, useEffect, useContext } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Bell,
  CheckCircle,
  XCircle,
  Eye,
  Calendar,
  Filter,
  Download,
} from "lucide-react";
import { LayoutContext } from "@/routes/__root";

interface AnalyticsMetric {
  label: string;
  value: number;
  change: number;
  trend: "up" | "down" | "neutral";
  format: "number" | "percentage" | "currency";
}

interface ChartData {
  name: string;
  value: number;
  percentage?: number;
}

interface TemplatePerformance {
  templateKey: string;
  type: string;
  sent: number;
  delivered: number;
  opened: number;
  deliveryRate: number;
  openRate: number;
}

const NotificationAnalytics = () => {
  const { setActiveTab } = useContext(LayoutContext);
  const [dateRange, setDateRange] = useState("7d");
  const [notificationType, setNotificationType] = useState("");
  const [userSegment, setUserSegment] = useState("");

  const [metrics, setMetrics] = useState<AnalyticsMetric[]>([]);
  const [deliveryData, setDeliveryData] = useState<ChartData[]>([]);
  const [typeBreakdown, setTypeBreakdown] = useState<ChartData[]>([]);
  const [templatePerformance, setTemplatePerformance] = useState<TemplatePerformance[]>([]);
  const [timeSeriesData, setTimeSeriesData] = useState<ChartData[]>([]);

  useEffect(() => {
    setActiveTab("notifications");
    loadAnalyticsData();
  }, [setActiveTab, dateRange, notificationType, userSegment]);

  const loadAnalyticsData = () => {
    // Mock data - replace with actual API calls
    setMetrics([
      {
        label: "Total Notifications",
        value: 12847,
        change: 12.5,
        trend: "up",
        format: "number",
      },
      {
        label: "Delivery Rate",
        value: 94.2,
        change: 2.1,
        trend: "up",
        format: "percentage",
      },
      {
        label: "Open Rate",
        value: 68.5,
        change: -1.3,
        trend: "down",
        format: "percentage",
      },
      {
        label: "Failed Deliveries",
        value: 745,
        change: -8.2,
        trend: "down",
        format: "number",
      },
    ]);

    setDeliveryData([
      { name: "Delivered", value: 12102, percentage: 94.2 },
      { name: "Failed", value: 745, percentage: 5.8 },
    ]);

    setTypeBreakdown([
      { name: "Voucher Created", value: 4523, percentage: 35.2 },
      { name: "Voucher Expiring", value: 3201, percentage: 24.9 },
      { name: "User Welcome", value: 2845, percentage: 22.1 },
      { name: "Order Created", value: 1456, percentage: 11.3 },
      { name: "Voucher Used", value: 822, percentage: 6.4 },
    ]);

    setTemplatePerformance([
      {
        templateKey: "voucher_created",
        type: "VOUCHER_CREATED",
        sent: 4523,
        delivered: 4301,
        opened: 2945,
        deliveryRate: 95.1,
        openRate: 68.5,
      },
      {
        templateKey: "voucher_expiring",
        type: "VOUCHER_EXPIRING",
        sent: 3201,
        delivered: 3089,
        opened: 2234,
        deliveryRate: 96.5,
        openRate: 72.3,
      },
      {
        templateKey: "user_welcome",
        type: "USER_WELCOME",
        sent: 2845,
        delivered: 2634,
        opened: 1587,
        deliveryRate: 92.6,
        openRate: 60.2,
      },
      {
        templateKey: "order_created",
        type: "ORDER_CREATED",
        sent: 1456,
        delivered: 1398,
        opened: 923,
        deliveryRate: 96.0,
        openRate: 66.0,
      },
    ]);

    setTimeSeriesData([
      { name: "Mon", value: 1234 },
      { name: "Tue", value: 1456 },
      { name: "Wed", value: 1789 },
      { name: "Thu", value: 1623 },
      { name: "Fri", value: 2134 },
      { name: "Sat", value: 1876 },
      { name: "Sun", value: 1534 },
    ]);
  };

  const formatValue = (value: number, format: string): string => {
    switch (format) {
      case "percentage":
        return `${value}%`;
      case "currency":
        return `₫${value.toLocaleString()}`;
      default:
        return value.toLocaleString();
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up":
        return "text-green-600";
      case "down":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <div className="min-h-full bg-gradient-to-br from-blue-50 to-indigo-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Notification Analytics
            </h1>
            <p className="text-gray-600 mt-2">
              Track delivery performance and user engagement
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Date Range
                </label>
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1d">Last 24 Hours</SelectItem>
                    <SelectItem value="7d">Last 7 Days</SelectItem>
                    <SelectItem value="30d">Last 30 Days</SelectItem>
                    <SelectItem value="90d">Last 90 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Notification Type
                </label>
                <Select value={notificationType} onValueChange={setNotificationType}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Types</SelectItem>
                    <SelectItem value="VOUCHER_CREATED">Voucher Created</SelectItem>
                    <SelectItem value="VOUCHER_EXPIRING">Voucher Expiring</SelectItem>
                    <SelectItem value="USER_WELCOME">User Welcome</SelectItem>
                    <SelectItem value="ORDER_CREATED">Order Created</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  User Segment
                </label>
                <Select value={userSegment} onValueChange={setUserSegment}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Users" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Users</SelectItem>
                    <SelectItem value="premium">Premium Users</SelectItem>
                    <SelectItem value="regular">Regular Users</SelectItem>
                    <SelectItem value="new">New Users</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {metrics.map((metric, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      {metric.label}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatValue(metric.value, metric.format)}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1">
                    {getTrendIcon(metric.trend)}
                  </div>
                </div>
                <div className="mt-2 flex items-center">
                  <span className={`text-sm font-medium ${getTrendColor(metric.trend)}`}>
                    {metric.change > 0 ? "+" : ""}{metric.change}%
                  </span>
                  <span className="text-sm text-gray-500 ml-2">vs last period</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Delivery Status Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span>Delivery Status</span>
              </CardTitle>
              <CardDescription>
                Overall notification delivery performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {deliveryData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-4 h-4 rounded-full ${
                          item.name === "Delivered" ? "bg-green-500" : "bg-red-500"
                        }`}
                      />
                      <span className="font-medium">{item.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{item.value.toLocaleString()}</div>
                      <div className="text-sm text-gray-500">{item.percentage}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Notification Type Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                <span>Type Breakdown</span>
              </CardTitle>
              <CardDescription>
                Distribution by notification type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {typeBreakdown.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.name}</span>
                      <span className="text-sm text-gray-500">
                        {item.value.toLocaleString()} ({item.percentage}%)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Template Performance */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="h-5 w-5 text-purple-600" />
              <span>Template Performance</span>
            </CardTitle>
            <CardDescription>
              Delivery and engagement rates by template
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Template</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">Sent</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">Delivered</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">Opened</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">Delivery Rate</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">Open Rate</th>
                  </tr>
                </thead>
                <tbody>
                  {templatePerformance.map((template, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium">
                            {template.templateKey.replace(/_/g, " ").toUpperCase()}
                          </div>
                          <Badge variant="secondary" className="text-xs mt-1">
                            {template.type.replace(/_/g, " ")}
                          </Badge>
                        </div>
                      </td>
                      <td className="text-right py-3 px-4 font-medium">
                        {template.sent.toLocaleString()}
                      </td>
                      <td className="text-right py-3 px-4">
                        {template.delivered.toLocaleString()}
                      </td>
                      <td className="text-right py-3 px-4">
                        {template.opened.toLocaleString()}
                      </td>
                      <td className="text-right py-3 px-4">
                        <span className={`font-medium ${
                          template.deliveryRate >= 95 ? "text-green-600" : 
                          template.deliveryRate >= 90 ? "text-yellow-600" : "text-red-600"
                        }`}>
                          {template.deliveryRate}%
                        </span>
                      </td>
                      <td className="text-right py-3 px-4">
                        <span className={`font-medium ${
                          template.openRate >= 70 ? "text-green-600" : 
                          template.openRate >= 60 ? "text-yellow-600" : "text-red-600"
                        }`}>
                          {template.openRate}%
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Time Series Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-indigo-600" />
              <span>Daily Notification Volume</span>
            </CardTitle>
            <CardDescription>
              Notification sending trends over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-end justify-between space-x-2">
              {timeSeriesData.map((item, index) => {
                const maxValue = Math.max(...timeSeriesData.map(d => d.value));
                const height = (item.value / maxValue) * 100;
                
                return (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div
                      className="w-full bg-indigo-500 rounded-t-sm hover:bg-indigo-600 transition-colors cursor-pointer"
                      style={{ height: `${height}%` }}
                      title={`${item.name}: ${item.value.toLocaleString()}`}
                    />
                    <div className="text-xs text-gray-600 mt-2">{item.name}</div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default NotificationAnalytics;
