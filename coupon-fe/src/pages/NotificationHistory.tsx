import { useState, useEffect, useContext, useMemo } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Search,
  Filter,
  ChevronUp,
  ChevronDown,
  Bell,
  FileText,
  Clock,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Trash2,
  RotateCcw,
  Download,
} from "lucide-react";
import { LayoutContext } from "@/routes/__root";

interface NotificationRecord {
  id: number;
  userId: number;
  type: string;
  title: string;
  message: string;
  status: "PENDING" | "SENT" | "FAILED" | "READ" | "CANCELLED";
  scheduledAt?: string;
  sentAt?: string;
  readAt?: string;
  createdAt: string;
  user?: {
    id: number;
    name: string;
    email: string;
  };
}

const NotificationHistory = () => {
  const { setActiveTab } = useContext(LayoutContext);
  const [notifications, setNotifications] = useState<NotificationRecord[]>([]);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [search, setSearch] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [sortConfig, setSortConfig] = useState<{
    key: keyof NotificationRecord | null;
    direction: "asc" | "desc";
  }>({ key: "createdAt", direction: "desc" });
  const [filters, setFilters] = useState({
    status: "",
    type: "",
    dateRange: "",
  });

  useEffect(() => {
    setActiveTab("notifications");
    // Mock data - replace with actual API call
    setNotifications([
      {
        id: 1,
        userId: 1,
        type: "VOUCHER_CREATED",
        title: "New Voucher Available: SAVE20",
        message: "A new voucher 'Winter Sale' worth ₫50,000 is now available! Use code SAVE20 before 2024-02-15.",
        status: "SENT",
        sentAt: "2024-01-15T14:30:00Z",
        readAt: "2024-01-15T15:45:00Z",
        createdAt: "2024-01-15T14:25:00Z",
        user: { id: 1, name: "John Doe", email: "<EMAIL>" },
      },
      {
        id: 2,
        userId: 2,
        type: "VOUCHER_EXPIRING",
        title: "Voucher Expiring Soon: WINTER50",
        message: "Your voucher WINTER50 will expire in 24 hours. Don't miss out on your savings!",
        status: "PENDING",
        scheduledAt: "2024-01-16T09:00:00Z",
        createdAt: "2024-01-15T12:00:00Z",
        user: { id: 2, name: "Jane Smith", email: "<EMAIL>" },
      },
      {
        id: 3,
        userId: 3,
        type: "USER_WELCOME",
        title: "Welcome to Coupon System, Alice!",
        message: "Welcome Alice! Thank you for joining our coupon system. Start saving money with exclusive deals.",
        status: "SENT",
        sentAt: "2024-01-15T10:15:00Z",
        createdAt: "2024-01-15T10:10:00Z",
        user: { id: 3, name: "Alice Johnson", email: "<EMAIL>" },
      },
      {
        id: 4,
        userId: 1,
        type: "VOUCHER_USED",
        title: "Voucher Used Successfully",
        message: "Your voucher SAVE20 has been applied to order #1234. You saved ₫50,000!",
        status: "READ",
        sentAt: "2024-01-14T16:20:00Z",
        readAt: "2024-01-14T16:25:00Z",
        createdAt: "2024-01-14T16:18:00Z",
        user: { id: 1, name: "John Doe", email: "<EMAIL>" },
      },
      {
        id: 5,
        userId: 4,
        type: "ORDER_CREATED",
        title: "Order Confirmation #1235",
        message: "Your order has been created successfully. Total amount: ₫150,000",
        status: "FAILED",
        createdAt: "2024-01-14T14:30:00Z",
        user: { id: 4, name: "Bob Wilson", email: "<EMAIL>" },
      },
    ]);
  }, [setActiveTab]);

  const notificationTypes = [
    { value: "VOUCHER_CREATED", label: "Voucher Created" },
    { value: "VOUCHER_EXPIRING", label: "Voucher Expiring" },
    { value: "VOUCHER_USED", label: "Voucher Used" },
    { value: "ORDER_CREATED", label: "Order Created" },
    { value: "VOUCHER_APPLIED", label: "Voucher Applied" },
    { value: "USER_WELCOME", label: "User Welcome" },
    { value: "USER_TYPE_UPGRADE", label: "User Type Upgrade" },
  ];

  const statusOptions = [
    { value: "PENDING", label: "Pending" },
    { value: "SENT", label: "Sent" },
    { value: "FAILED", label: "Failed" },
    { value: "READ", label: "Read" },
    { value: "CANCELLED", label: "Cancelled" },
  ];

  // Filter and search logic
  const filteredNotifications = useMemo(() => {
    return notifications.filter(notification => {
      const matchesSearch = search === "" || 
        notification.title.toLowerCase().includes(search.toLowerCase()) ||
        notification.message.toLowerCase().includes(search.toLowerCase()) ||
        notification.user?.name.toLowerCase().includes(search.toLowerCase()) ||
        notification.user?.email.toLowerCase().includes(search.toLowerCase());

      const matchesStatus = filters.status === "" || notification.status === filters.status;
      const matchesType = filters.type === "" || notification.type === filters.type;

      return matchesSearch && matchesStatus && matchesType;
    });
  }, [notifications, search, filters]);

  // Sort logic
  const sortedNotifications = useMemo(() => {
    if (!sortConfig.key) return filteredNotifications;

    return [...filteredNotifications].sort((a, b) => {
      const aValue = a[sortConfig.key!];
      const bValue = b[sortConfig.key!];

      if (aValue < bValue) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }
      return 0;
    });
  }, [filteredNotifications, sortConfig]);

  // Pagination logic
  const paginatedNotifications = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedNotifications.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedNotifications, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(sortedNotifications.length / itemsPerPage);

  const handleSort = (key: keyof NotificationRecord) => {
    setSortConfig(current => ({
      key,
      direction: current.key === key && current.direction === "asc" ? "desc" : "asc"
    }));
  };

  const handleSelectAll = () => {
    if (selectedIds.length === paginatedNotifications.length) {
      setSelectedIds([]);
    } else {
      setSelectedIds(paginatedNotifications.map(n => n.id));
    }
  };

  const handleSelectOne = (id: number) => {
    setSelectedIds(current => 
      current.includes(id) 
        ? current.filter(selectedId => selectedId !== id)
        : [...current, id]
    );
  };

  const handleBulkAction = (action: string) => {
    if (selectedIds.length > 0) {
      console.log(`Bulk action: ${action}`, selectedIds);
      // Implement bulk actions here
      setSelectedIds([]);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      PENDING: "bg-yellow-100 text-yellow-800",
      SENT: "bg-green-100 text-green-800",
      FAILED: "bg-red-100 text-red-800",
      READ: "bg-blue-100 text-blue-800",
      CANCELLED: "bg-gray-100 text-gray-800",
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-100 text-gray-800"}>
        {status}
      </Badge>
    );
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      VOUCHER_CREATED: <FileText className="h-4 w-4 text-blue-600" />,
      VOUCHER_EXPIRING: <Clock className="h-4 w-4 text-orange-600" />,
      VOUCHER_USED: <CheckCircle className="h-4 w-4 text-green-600" />,
      ORDER_CREATED: <Bell className="h-4 w-4 text-purple-600" />,
      VOUCHER_APPLIED: <CheckCircle className="h-4 w-4 text-indigo-600" />,
      USER_WELCOME: <Users className="h-4 w-4 text-emerald-600" />,
      USER_TYPE_UPGRADE: <Users className="h-4 w-4 text-yellow-600" />,
    };
    
    return icons[type as keyof typeof icons] || <Bell className="h-4 w-4 text-gray-600" />;
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className="min-h-full bg-gradient-to-br from-blue-50 to-indigo-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Notification History
            </h1>
            <p className="text-gray-600 mt-2">
              View and manage all notification records
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search notifications..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Status</SelectItem>
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={filters.type} onValueChange={(value) => setFilters(prev => ({ ...prev, type: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Types</SelectItem>
                  {notificationTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={filters.dateRange} onValueChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Date Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedIds.length > 0 && (
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {selectedIds.length} notification{selectedIds.length > 1 ? 's' : ''} selected
                </span>
                <div className="flex space-x-2">
                  <Button size="sm" onClick={() => handleBulkAction("resend")}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Resend
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleBulkAction("cancel")}>
                    <XCircle className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button size="sm" variant="destructive" onClick={() => handleBulkAction("delete")}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Notifications Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center space-x-2">
                <Bell className="h-5 w-5 text-blue-600" />
                <span>Notifications ({sortedNotifications.length})</span>
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedIds.length === paginatedNotifications.length && paginatedNotifications.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead className="w-12">Type</TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("title")}
                    >
                      <div className="flex items-center space-x-2">
                        <span>Title</span>
                        {sortConfig.key === "title" && (
                          sortConfig.direction === "asc" ? 
                            <ChevronUp className="h-4 w-4" /> : 
                            <ChevronDown className="h-4 w-4" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Recipient</TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("status")}
                    >
                      <div className="flex items-center space-x-2">
                        <span>Status</span>
                        {sortConfig.key === "status" && (
                          sortConfig.direction === "asc" ? 
                            <ChevronUp className="h-4 w-4" /> : 
                            <ChevronDown className="h-4 w-4" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("createdAt")}
                    >
                      <div className="flex items-center space-x-2">
                        <span>Created</span>
                        {sortConfig.key === "createdAt" && (
                          sortConfig.direction === "asc" ? 
                            <ChevronUp className="h-4 w-4" /> : 
                            <ChevronDown className="h-4 w-4" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Sent/Scheduled</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedNotifications.map((notification) => (
                    <TableRow key={notification.id} className="hover:bg-gray-50">
                      <TableCell>
                        <Checkbox
                          checked={selectedIds.includes(notification.id)}
                          onCheckedChange={() => handleSelectOne(notification.id)}
                        />
                      </TableCell>
                      <TableCell>
                        {getTypeIcon(notification.type)}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium text-sm">{notification.title}</div>
                          <div className="text-xs text-gray-500 truncate max-w-xs">
                            {notification.message}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">{notification.user?.name}</div>
                          <div className="text-gray-500">{notification.user?.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(notification.status)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {formatTimestamp(notification.createdAt)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {notification.sentAt ? 
                          formatTimestamp(notification.sentAt) : 
                          notification.scheduledAt ? 
                            `Scheduled: ${formatTimestamp(notification.scheduledAt)}` : 
                            "-"
                        }
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          {notification.status === "FAILED" && (
                            <Button variant="ghost" size="sm">
                              <RotateCcw className="h-4 w-4" />
                            </Button>
                          )}
                          <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Show</span>
                  <Select value={itemsPerPage.toString()} onValueChange={(value) => {
                    setItemsPerPage(Number(value));
                    setCurrentPage(1);
                  }}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-gray-600">
                    of {sortedNotifications.length} notifications
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(totalPages, 5) }).map((_, i) => {
                      const pageNumber = currentPage <= 3 
                        ? i + 1 
                        : currentPage >= totalPages - 2 
                          ? totalPages - 4 + i 
                          : currentPage - 2 + i;

                      if (pageNumber < 1 || pageNumber > totalPages) return null;

                      return (
                        <Button
                          key={pageNumber}
                          variant={currentPage === pageNumber ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(pageNumber)}
                        >
                          {pageNumber}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default NotificationHistory;
