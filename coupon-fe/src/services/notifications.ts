import { $api } from "./common";
import { snakeCaseKeys } from "@/lib/snakeCase";
import type {
  ListNotificationsRequest,
  ListNotificationsResponse,
  NotificationResponse
} from "@/types/notifications";

// Admin notification interfaces
export interface NotificationTemplate {
  id: number;
  templateKey: string;
  type: string;
  titleTemplate: string;
  messageTemplate: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTemplateRequest {
  templateKey: string;
  type: string;
  titleTemplate: string;
  messageTemplate: string;
  isActive: boolean;
}

export interface UpdateTemplateRequest extends CreateTemplateRequest {
  id: number;
}

export interface SendNotificationRequest {
  templateId?: number;
  title: string;
  message: string;
  userIds?: number[];
  userTypes?: string[];
  scheduledAt?: string;
}

export interface NotificationAnalytics {
  totalNotifications: number;
  deliveryRate: number;
  openRate: number;
  failedDeliveries: number;
  deliveryData: Array<{ name: string; value: number; percentage: number }>;
  typeBreakdown: Array<{ name: string; value: number; percentage: number }>;
  templatePerformance: Array<{
    templateKey: string;
    type: string;
    sent: number;
    delivered: number;
    opened: number;
    deliveryRate: number;
    openRate: number;
  }>;
}

export interface UseListNotificationsParams extends ListNotificationsRequest {}

export function useListNotifications() {
  const query = $api.useQuery("get", "/api/notifications");
  return {
    ...query,
    data: query.data as ListNotificationsResponse | undefined
  };
}

export function useUpdateNotificationStatus() {
  return $api.useMutation("put", "/api/notifications/{id}/status");
}

// Admin notification template management
export function useListTemplates() {
  const query = $api.useQuery("get", "/api/admin/notification-templates");
  return {
    ...query,
    data: query.data as NotificationTemplate[] | undefined
  };
}

export function useCreateTemplate() {
  return $api.useMutation("post", "/api/admin/notification-templates", {
    onSuccess: () => {
      // Invalidate templates list
      $api.queryClient.invalidateQueries({ queryKey: ["get", "/api/admin/notification-templates"] });
    }
  });
}

export function useUpdateTemplate() {
  return $api.useMutation("put", "/api/admin/notification-templates/{id}", {
    onSuccess: () => {
      // Invalidate templates list
      $api.queryClient.invalidateQueries({ queryKey: ["get", "/api/admin/notification-templates"] });
    }
  });
}

export function useDeleteTemplate() {
  return $api.useMutation("delete", "/api/admin/notification-templates/{id}", {
    onSuccess: () => {
      // Invalidate templates list
      $api.queryClient.invalidateQueries({ queryKey: ["get", "/api/admin/notification-templates"] });
    }
  });
}

// Admin notification sending
export function useSendNotification() {
  return $api.useMutation("post", "/api/admin/notifications/send");
}

// Admin notification analytics
export function useNotificationAnalytics(params: { dateRange?: string; type?: string; userSegment?: string } = {}) {
  const query = $api.useQuery("get", "/api/admin/notifications/analytics", {
    params: { query: snakeCaseKeys(params) },
  });
  return {
    ...query,
    data: query.data as NotificationAnalytics | undefined
  };
}

// Admin notification history (simplified)
export function useAdminListNotifications() {
  const query = $api.useQuery("get", "/api/admin/notifications");
  return {
    ...query,
    data: query.data as ListNotificationsResponse | undefined
  };
}

// Bulk operations
export function useBulkUpdateNotifications() {
  return $api.useMutation("put", "/api/admin/notifications/bulk", {
    onSuccess: () => {
      // Invalidate notifications list
      $api.queryClient.invalidateQueries({ queryKey: ["get", "/api/admin/notifications"] });
    }
  });
}

export function useBulkDeleteNotifications() {
  return $api.useMutation("delete", "/api/admin/notifications/bulk", {
    onSuccess: () => {
      // Invalidate notifications list
      $api.queryClient.invalidateQueries({ queryKey: ["get", "/api/admin/notifications"] });
    }
  });
}
